"""initial

Revision ID: 278e8f10630b
Revises: 87200b406a61
Create Date: 2025-06-10 18:18:18.982785

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '278e8f10630b'
down_revision = '87200b406a61'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('activity_log', schema=None) as batch_op:
        batch_op.add_column(sa.Column('entity_type', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('entity_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('ip_address', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('user_agent', sa.Text(), nullable=True))
        batch_op.alter_column('details',
               existing_type=mysql.TEXT(),
               type_=sa.JSON(),
               existing_nullable=True)

    with op.batch_alter_table('attachment', schema=None) as batch_op:
        batch_op.add_column(sa.Column('file_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('file_size', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('file_type', sa.String(length=50), nullable=True))

    with op.batch_alter_table('external_task_mapping', schema=None) as batch_op:
        batch_op.add_column(sa.Column('external_board_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('sync_status', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('sync_errors', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))

    with op.batch_alter_table('integration', schema=None) as batch_op:
        batch_op.add_column(sa.Column('platform_user_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('is_active', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('settings', sa.JSON(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))

    with op.batch_alter_table('notification', schema=None) as batch_op:
        batch_op.add_column(sa.Column('title', sa.String(length=255), nullable=False))
        batch_op.add_column(sa.Column('type', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('priority', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('read_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('related_entity_type', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('related_entity_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('action_url', sa.String(length=255), nullable=True))

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('resource', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('action', sa.String(length=50), nullable=True))

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('priority',
               existing_type=mysql.VARCHAR(length=50),
               type_=sa.String(length=20),
               existing_nullable=True)
        batch_op.alter_column('user_id',
               existing_type=mysql.INTEGER(),
               nullable=True)

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('granted_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('granted_by', sa.Integer(), nullable=True))
        batch_op.create_foreign_key(None, 'user', ['granted_by'], ['id'])

    with op.batch_alter_table('setting', schema=None) as batch_op:
        batch_op.add_column(sa.Column('category', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('data_type', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('is_encrypted', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=True))

    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('github_id', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('github_username', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('github_avatar_url', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('auth_provider', sa.String(length=50), nullable=True))
        batch_op.alter_column('password_hash',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
        batch_op.create_unique_constraint(None, ['github_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('password_hash',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
        batch_op.drop_column('auth_provider')
        batch_op.drop_column('github_avatar_url')
        batch_op.drop_column('github_username')
        batch_op.drop_column('github_id')

    with op.batch_alter_table('setting', schema=None) as batch_op:
        batch_op.drop_column('description')
        batch_op.drop_column('is_encrypted')
        batch_op.drop_column('data_type')
        batch_op.drop_column('category')

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('granted_by')
        batch_op.drop_column('granted_at')

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('user_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
        batch_op.alter_column('priority',
               existing_type=sa.String(length=20),
               type_=mysql.VARCHAR(length=50),
               existing_nullable=True)

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.drop_column('action')
        batch_op.drop_column('resource')

    with op.batch_alter_table('notification', schema=None) as batch_op:
        batch_op.drop_column('action_url')
        batch_op.drop_column('related_entity_id')
        batch_op.drop_column('related_entity_type')
        batch_op.drop_column('read_at')
        batch_op.drop_column('priority')
        batch_op.drop_column('type')
        batch_op.drop_column('title')

    with op.batch_alter_table('integration', schema=None) as batch_op:
        batch_op.drop_column('updated_at')
        batch_op.drop_column('settings')
        batch_op.drop_column('is_active')
        batch_op.drop_column('platform_user_id')

    with op.batch_alter_table('external_task_mapping', schema=None) as batch_op:
        batch_op.drop_column('created_at')
        batch_op.drop_column('sync_errors')
        batch_op.drop_column('sync_status')
        batch_op.drop_column('external_board_id')

    with op.batch_alter_table('attachment', schema=None) as batch_op:
        batch_op.drop_column('file_type')
        batch_op.drop_column('file_size')
        batch_op.drop_column('file_name')

    with op.batch_alter_table('activity_log', schema=None) as batch_op:
        batch_op.alter_column('details',
               existing_type=sa.JSON(),
               type_=mysql.TEXT(),
               existing_nullable=True)
        batch_op.drop_column('user_agent')
        batch_op.drop_column('ip_address')
        batch_op.drop_column('entity_id')
        batch_op.drop_column('entity_type')

    # ### end Alembic commands ###
